#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
主要路由模块
包含首页、设备管理和OTA相关的路由
"""

import os
import time
import json
from datetime import datetime
from threading import Thread
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename

from models.device import Device
from models.ota_task import OtaTask
from models.firmware import Firmware
from models.database import db
from services.iot_client_manager import IoTClientManager
from services.ota_service import OtaTaskThreadManager
from utils.logger import LoggerManager
from utils.socket_manager import emit_task_update
from services.cache_service import cache_service, CacheKeyManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
main_bp = Blueprint('main', __name__)

# 获取OTA任务管理器实例
ota_task_manager = OtaTaskThreadManager()

@main_bp.route('/liquid-glass-test')
@login_required
def liquid_glass_test():
    """液态玻璃主题测试页面"""
    return render_template('liquid-glass-test.html')

@main_bp.route('/')
@login_required
def index():
    """首页"""
    devices = Device.query.all()
    firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()

    # 从应用上下文获取设备状态缓存
    device_status_cache = current_app.config.get('DEVICE_STATUS_CACHE', {})
    device_status_lock = current_app.config.get('DEVICE_STATUS_LOCK')

    with device_status_lock:
        current_timestamp = int(datetime.now().timestamp())
        # 确保所有设备都有状态记录
        for device in devices:
            if device.id not in device_status_cache:
                device_status_cache[device.id] = {
                    'is_online': False,
                    'last_check': current_timestamp,
                }

    return render_template('index.html', devices=devices, firmwares=firmwares, device_status_cache=device_status_cache)

@main_bp.route('/ota/tasks')
@login_required
def ota_tasks():
    """OTA任务列表页面"""
    # 尝试从缓存获取固件列表
    firmwares = cache_service.get(CacheKeyManager.FIRMWARE_LIST)
    if firmwares is None:
        firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()
        # 缓存固件列表，超时时间10分钟
        cache_service.set(CacheKeyManager.FIRMWARE_LIST, firmwares, timeout=600)
        logger.debug("固件列表已缓存")

    # 尝试从缓存获取任务统计信息
    stats = cache_service.get(CacheKeyManager.OTA_TASK_STATS)
    if stats is None:
        # 优化：使用单次聚合查询替代多次查询
        from sqlalchemy import func, case
        stats_result = db.session.query(
            func.count(OtaTask.id).label('total'),
            func.sum(case((OtaTask.status == '成功', 1), else_=0)).label('success'),
            func.sum(case((OtaTask.status == '失败', 1), else_=0)).label('failed'),
            func.sum(case((OtaTask.status == '进行中', 1), else_=0)).label('in_progress'),
            func.sum(case((OtaTask.status == '等待中', 1), else_=0)).label('waiting')
        ).first()

        stats = {
            'total': stats_result.total or 0,
            'success': stats_result.success or 0,
            'failed': stats_result.failed or 0,
            'in_progress': stats_result.in_progress or 0,
            'waiting': stats_result.waiting or 0
        }
        # 缓存统计信息，超时时间30秒（因为任务状态变化较频繁）
        cache_service.set(CacheKeyManager.OTA_TASK_STATS, stats, timeout=30)
        logger.debug("OTA任务统计信息已缓存")

    # 传递空的任务列表和统计信息
    return render_template('ota_tasks.html',
                         tasks=[],
                         firmwares=firmwares,
                         stats=stats)

@main_bp.route('/api/ota/tasks')
@login_required
def get_ota_tasks_api():
    """获取OTA任务列表API（支持分页、搜索、筛选）"""
    try:
        # 导入必要的模型
        from models.firmware import Firmware
        from datetime import datetime

        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 获取搜索和筛选参数
        filters = {
            'search': request.args.get('search', '').strip(),
            'status': request.args.get('status', 'all'),
            'device_id': request.args.get('device_id', '').strip(),
            'firmware': request.args.get('firmware', '').strip(),
            'firmware_version': request.args.get('firmware_version', '').strip(),
            'crc32': request.args.get('crc32', '').strip(),
            'date': request.args.get('date', '').strip()
        }

        # 生成缓存键
        cache_key = CacheKeyManager.get_ota_task_list_key(page, per_page, **filters)

        # 尝试从缓存获取结果
        cached_result = cache_service.get(cache_key)
        if cached_result is not None:
            logger.debug(f"OTA任务列表缓存命中: {cache_key}")
            return jsonify(cached_result)

        # 从filters字典中提取参数
        search = filters['search']
        status_filter = filters['status']
        device_id_filter = filters['device_id']
        firmware_filter = filters['firmware']
        firmware_version_filter = filters['firmware_version']
        crc32_filter = filters['crc32']
        date_filter = filters['date']

        # 构建查询 - 不使用JOIN，因为device_id可能不匹配
        query = OtaTask.query

        # 搜索条件（任务ID或设备ID）
        if search:
            # 优化搜索：使用数字搜索任务ID，字符串搜索设备ID
            search_conditions = []
            if search.isdigit():
                search_conditions.append(OtaTask.id == int(search))
            search_conditions.append(OtaTask.device_id.contains(search))
            query = query.filter(db.or_(*search_conditions))

        # 状态筛选
        if status_filter and status_filter != 'all':
            query = query.filter(OtaTask.status == status_filter)

        # 设备ID筛选
        if device_id_filter:
            query = query.filter(OtaTask.device_id.contains(device_id_filter))

        # 固件版本筛选（兼容旧参数名）
        if firmware_filter:
            query = query.filter(OtaTask.firmware_version.contains(firmware_filter))

        # 固件版本筛选（新参数名，支持模糊匹配）
        if firmware_version_filter:
            query = query.filter(OtaTask.firmware_version.contains(firmware_version_filter))

        # CRC32筛选（需要关联固件表）
        if crc32_filter:
            # 通过固件版本关联固件表进行CRC32筛选
            query = query.join(Firmware, OtaTask.firmware_version == Firmware.version)
            # 支持精确匹配和部分匹配
            if len(crc32_filter) == 8:
                # 8位完整CRC32，精确匹配
                query = query.filter(Firmware.crc32 == crc32_filter.upper())
            else:
                # 部分匹配，不区分大小写
                query = query.filter(Firmware.crc32.contains(crc32_filter.upper()))

        # 日期筛选
        if date_filter:
            try:
                filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
                # 优化日期筛选：使用日期范围而不是函数
                start_datetime = datetime.combine(filter_date, datetime.min.time())
                end_datetime = datetime.combine(filter_date, datetime.max.time())
                query = query.filter(
                    OtaTask.created_at >= start_datetime,
                    OtaTask.created_at <= end_datetime
                )
            except ValueError:
                pass  # 忽略无效的日期格式

        # 按创建时间降序排列
        query = query.order_by(OtaTask.created_at.desc())

        # 执行分页查询
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # 优化：批量获取相关数据，避免N+1查询问题
        tasks_data = []

        # 批量获取固件信息
        firmware_paths = [task.firmware_path for task in pagination.items if task.firmware_path]
        firmwares = {}
        if firmware_paths:
            firmware_list = Firmware.query.filter(Firmware.file_path.in_(firmware_paths)).all()
            firmwares = {f.file_path: f for f in firmware_list}

        # 批量获取设备信息
        device_ids = [task.device_id for task in pagination.items]
        devices = {}
        if device_ids:
            device_list = Device.query.filter(Device.id.in_(device_ids)).all()
            devices = {d.id: d for d in device_list}

        # 构建返回数据
        for task in pagination.items:
            # 获取固件CRC信息
            firmware_crc = None
            if task.firmware_path and task.firmware_path in firmwares:
                firmware_crc = firmwares[task.firmware_path].crc32

            # 获取设备信息
            device = devices.get(task.device_id)
            device_id = device.device_id if device else str(task.device_id)
            device_name = device.device_id if device else str(task.device_id)

            tasks_data.append({
                'id': task.id,
                'device_id': device_id,
                'device_name': device_name,
                'firmware_version': task.firmware_version,
                'firmware_crc': firmware_crc,
                'status': task.status,
                'progress': task.progress,
                'error_message': task.error_message or '',
                'created_at': task.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': task.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })

        # 构建返回结果
        result = {
            'success': True,
            'tasks': tasks_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev,
                'next_num': pagination.next_num,
                'prev_num': pagination.prev_num
            }
        }

        # 缓存结果，超时时间30秒（因为任务状态变化较频繁）
        cache_service.set(cache_key, result, timeout=30)
        logger.debug(f"OTA任务列表已缓存: {cache_key}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取OTA任务列表失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main_bp.route('/ota/start', methods=['POST'])
@login_required
def start_ota():
    """开始OTA升级"""
    from services.ota_service import start_ota_task

    try:
        # 获取请求数据
        if request.is_json:
            data = request.get_json()
            device_ids = data.get('device_ids', [])
            firmware_id = data.get('firmware_id')

            if not device_ids or not firmware_id:
                return jsonify({'success': False, 'message': '缺少必要参数'})

            # 调用并行OTA服务
            success, message = start_ota_task(device_ids, firmware_id=firmware_id)

        else:
            # 兼容旧的上传文件方式
            device_ids = request.form.getlist('device_ids[]')
            firmware_file = request.files.get('firmware_file')

            if not device_ids or not firmware_file:
                return jsonify({'success': False, 'message': '缺少必要参数'})

            # 调用并行OTA服务
            success, message = start_ota_task(device_ids, firmware_file=firmware_file)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        logger.error(f"创建OTA任务失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@main_bp.route('/ota/task/delete/<int:id>')
@login_required
def delete_task(id):
    """删除OTA任务（兼容旧版本）"""
    from models.database import db

    task = OtaTask.query.get_or_404(id)

    try:
        # 如果任务处于进行中，则返回进行中的任务不允许删除
        if task.status == "进行中":
            flash('进行中的任务删除后仍然会继续执行！', 'danger')

        # 删除任务记录
        db.session.delete(task)
        db.session.commit()

        flash('任务删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除任务失败: {e}")
        flash('删除任务失败', 'danger')

    return redirect(url_for('main.ota_tasks'))

@main_bp.route('/api/ota/task/delete/<int:id>', methods=['DELETE'])
@login_required
def delete_task_api(id):
    """删除OTA任务API"""
    from models.database import db

    try:
        task = OtaTask.query.get_or_404(id)

        # 如果任务处于进行中，给出警告但允许删除
        warning_message = None
        if task.status == "进行中":
            warning_message = "进行中的任务删除后仍然会继续执行！"

        # 删除任务记录
        db.session.delete(task)
        db.session.commit()

        response_data = {
            'success': True,
            'message': '任务删除成功'
        }

        if warning_message:
            response_data['warning'] = warning_message

        return jsonify(response_data)

    except Exception as e:
        db.session.rollback()
        logger.error(f"删除任务失败: {e}")
        return jsonify({'success': False, 'message': f'删除任务失败: {str(e)}'}), 500

@main_bp.route('/api/ota/tasks/stats')
@login_required
def get_ota_tasks_stats():
    """获取OTA任务统计信息API"""
    try:
        total_tasks = OtaTask.query.count()
        success_count = OtaTask.query.filter_by(status='成功').count()
        failed_count = OtaTask.query.filter_by(status='失败').count()
        in_progress_count = OtaTask.query.filter_by(status='进行中').count()
        waiting_count = OtaTask.query.filter_by(status='等待中').count()

        return jsonify({
            'success': True,
            'stats': {
                'total': total_tasks,
                'success': success_count,
                'failed': failed_count,
                'in_progress': in_progress_count,
                'waiting': waiting_count
            }
        })

    except Exception as e:
        logger.error(f"获取OTA任务统计失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main_bp.route('/ota/task/<int:task_id>/retry', methods=['POST'])
@login_required
def retry_task(task_id):
    """重试OTA任务"""
    from services.ota_service import retry_ota_task

    try:
        success, message = retry_ota_task(task_id)
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        logger.error(f"重试任务失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@main_bp.route('/ota/task/<int:task_id>/pause', methods=['POST'])
@login_required
def pause_task(task_id):
    """暂停OTA任务"""
    from services.ota_service import pause_ota_task

    try:
        success, message = pause_ota_task(task_id)
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        logger.error(f"暂停任务失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@main_bp.route('/ota/task/<int:task_id>/resume', methods=['POST'])
@login_required
def resume_task(task_id):
    """恢复OTA任务"""
    from services.ota_pause_manager import resume_ota_task_enhanced

    try:
        success = resume_ota_task_enhanced(task_id)

        if success:
            return jsonify({'success': True, 'message': f'任务 {task_id} 恢复请求已处理'})
        else:
            return jsonify({'success': False, 'message': f'任务 {task_id} 恢复失败'})
    except Exception as e:
        logger.error(f"恢复任务失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@main_bp.route('/ota/task/<int:task_id>/cancel', methods=['POST'])
@login_required
def cancel_task(task_id):
    """取消OTA任务"""
    from services.ota_service import cancel_ota_task

    try:
        success, message = cancel_ota_task(task_id)
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@main_bp.route('/api/ota/stats')
@login_required
def get_ota_stats():
    """获取OTA服务统计信息"""
    from services.ota_service import get_ota_service_stats

    try:
        stats = get_ota_service_stats()
        return jsonify({'success': True, 'stats': stats})
    except Exception as e:
        logger.error(f"获取OTA统计信息失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@main_bp.route('/api/websocket/test')
@login_required
def test_websocket():
    """测试WebSocket连接"""
    from utils.websocket_test import send_test_message, check_websocket_status, test_direct_websocket_emit

    try:
        # 检查WebSocket状态
        status_ok = check_websocket_status()

        # 发送测试消息
        test_id = int(time.time()) % 100000
        message_sent = send_test_message(test_id)

        # 测试直接发送
        direct_sent = test_direct_websocket_emit(test_id + 1)

        return jsonify({
            'success': True,
            'status': {
                'socketio_status': status_ok,
                'test_message_sent': message_sent,
                'direct_message_sent': direct_sent,
                'test_task_ids': [test_id, test_id + 1]
            },
            'message': 'WebSocket测试完成，请检查前端是否收到消息'
        })
    except Exception as e:
        logger.error(f"WebSocket测试失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@main_bp.route('/test_websocket')
@login_required
def test_websocket1():
    """测试WebSocket连接"""
    try:
        with current_app.app_context():
            emit_task_update(999, "测试", 50, "这是一个测试消息")
            logger.info("测试消息已发送")
        return jsonify({'success': True, 'message': '测试消息已发送'})
    except Exception as e:
        logger.error(f"发送测试消息失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@main_bp.route('/theme-test')
@login_required
def theme_test():
    """主题测试页面"""
    return render_template('theme-test.html')
